# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Iterable
from typing_extensions import Literal, Required, TypedDict

__all__ = ["RealtimeConversationItemUserMessageParam", "Content"]


class Content(TypedDict, total=False):
    audio: str
    """
    Base64-encoded audio bytes (for `input_audio`), these will be parsed as the
    format specified in the session input audio type configuration. This defaults to
    PCM 16-bit 24kHz mono if not specified.
    """

    detail: Literal["auto", "low", "high"]
    """The detail level of the image (for `input_image`).

    `auto` will default to `high`.
    """

    image_url: str
    """Base64-encoded image bytes (for `input_image`) as a data URI.

    For example `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...`. Supported
    formats are PNG and JPEG.
    """

    text: str
    """The text content (for `input_text`)."""

    transcript: str
    """Transcript of the audio (for `input_audio`).

    This is not sent to the model, but will be attached to the message item for
    reference.
    """

    type: Literal["input_text", "input_audio", "input_image"]
    """The content type (`input_text`, `input_audio`, or `input_image`)."""


class RealtimeConversationItemUserMessageParam(TypedDict, total=False):
    content: Required[Iterable[Content]]
    """The content of the message."""

    role: Required[Literal["user"]]
    """The role of the message sender. Always `user`."""

    type: Required[Literal["message"]]
    """The type of the item. Always `message`."""

    id: str
    """The unique ID of the item.

    This may be provided by the client or generated by the server.
    """

    object: Literal["realtime.item"]
    """Identifier for the API object being returned - always `realtime.item`.

    Optional when creating a new item.
    """

    status: Literal["completed", "incomplete", "in_progress"]
    """The status of the item. Has no effect on the conversation."""
